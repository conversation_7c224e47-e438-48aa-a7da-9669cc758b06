from fastapi import APIRouter, UploadFile, File, HTTPException, Query
from controllers.ocr_controller import process_cambodian_id_ocr
from schemas.ocr import CambodianIDCardOCRResult

router = APIRouter(prefix="/ocr", tags=["ocr"])

@router.post("/idcard", response_model=CambodianIDCardOCRResult)
async def ocr_idcard(
    file: UploadFile = File(...),
    enhanced_preprocessing: bool = Query(True, description="Use enhanced preprocessing pipeline for better OCR results")
):
    """
    Extract text from Cambodian ID card images using OCR.

    Args:
        file: Image file (JPEG, PNG, etc.)
        enhanced_preprocessing: Whether to use the enhanced preprocessing pipeline

    Returns:
        Structured OCR results with extracted fields
    """
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload an image.")

    result = await process_cambodian_id_ocr(file, use_enhanced_preprocessing=enhanced_preprocessing)
    return result
